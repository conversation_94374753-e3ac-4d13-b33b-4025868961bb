# 订单管理导出功能API文档

## 概述
本文档描述了订单管理模块的导出功能API接口，包括订单明细导出功能。

## API接口

### 1. 导出订单明细

**接口地址：** `GET /order-open/manage/export/orderDetail`

**接口描述：** 导出订单明细数据到Excel文件

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| startDate | String | 否 | 下单开始日期，格式：yyyy-MM-dd | 2024-01-01 |
| endDate | String | 否 | 下单结束日期，格式：yyyy-MM-dd | 2024-12-31 |
| productName | String | 否 | 商品名称（模糊搜索） | 苹果 |
| orderId | String | 否 | 订单编号（精确搜索） | ********* |
| orderStatus | List<Integer> | 否 | 订单状态列表 | [1,2,3] |
| storeFrontName | String | 否 | 门店名称（模糊搜索） | 旗舰店 |
| accountName | String | 否 | 用户名称（模糊搜索） | 张三 |
| userName | String | 否 | 买家昵称（模糊搜索） | 小明 |
| isRefund | Integer | 否 | 是否退款：-1所有订单，0否，1是 | 0 |
| phone | String | 否 | 买家电话（模糊搜索） | 138 |
| note | String | 否 | 备注（模糊搜索） | 备注信息 |
| receiverPhone | String | 否 | 收货人电话（模糊搜索） | 139 |

**订单状态说明：**
- -1：所有订单
- 0：待付款（待买家付款）
- 1：待发货（买家已付款）
- 2：配送中（卖家已发货）
- 3：待提货（商家直配已到达提货点或物流订单已发货）
- 4：已完成（用户已经签收）
- 5：已关闭

**请求示例：**
```
GET /order-open/manage/export/orderDetail?startDate=2024-01-01&endDate=2024-12-31&orderStatus=1,2,3
```

**响应说明：**
- 成功：直接下载Excel文件
- 失败：返回错误信息

**导出字段说明：**

| 字段名 | 描述 | 数据类型 |
|--------|------|----------|
| 序号 | 自动生成的序号 | 数字 |
| 订单号 | 订单编号 | 文本 |
| 商品名称 | 商品的名称 | 文本 |
| 单价 | 商品单价 | 金额 |
| 数量 | 购买数量 | 数字 |
| 金额 | 实际金额 | 金额 |
| 门店名称 | 所属门店名称 | 文本 |
| 客户名称 | 买家昵称 | 文本 |
| 电话号码 | 买家电话 | 文本 |
| 职员名称 | 账户名称 | 文本 |
| 发货方式 | 配送方式 | 文本 |
| 发货仓库 | 仓库名称 | 文本 |

**发货方式说明：**
- 手动发货
- 物流配送
- 送货上门
- 无需物流配送
- 自提

## 技术实现

### 数据源
导出功能直接调用现有的订单明细查询方法 `searchOrderDetail`，确保导出数据与查询页面数据完全一致。

### 实现方式
1. 调用现有查询方法获取 `ManageOrderItemVo` 数据
2. 转换为 `ManageOrderItemExcelVo` 导出格式
3. 自动添加序号字段
4. 转换发货方式枚举为中文描述
5. 使用 `HuToolExcelUtils.exportData` 导出Excel

### 导出限制
- 最大导出数量：由系统配置 `CommonConstants.MAX_EXPORT_SIZE` 控制
- 文件格式：Excel (.xlsx)
- 文件命名：`yyyyMMdd HHmmss` + 4位随机字符 + "_订单明细列表"

### 错误处理
- 导出数据异常时返回错误码和错误信息
- 参数验证失败时返回相应的错误提示

## 注意事项

1. 导出功能会自动添加序号字段
2. 发货方式会自动转换为中文描述
3. 导出数据基于当前用户的权限范围
4. 大量数据导出可能需要较长时间，请耐心等待
5. 建议在数据量较大时使用时间范围等条件进行筛选

## 更新日志

- 2025-01-04：初始版本，实现订单明细导出功能
- 2025-01-04：优化实现方式，直接调用现有查询方法确保数据一致性

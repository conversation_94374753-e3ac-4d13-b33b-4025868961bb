# 评价管理导出API文档

## 接口信息

**接口名称：** 商家导出评价列表  
**接口地址：** `GET /order-open/manage/evaluate/export`  
**接口描述：** 根据查询条件导出评价管理列表数据到Excel文件

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| goodsName | String | 否 | 商品名称，模糊搜索 | "苹果" |
| orderId | String | 否 | 订单编号，精确搜索 | "202501040001" |
| payTimeStart | String | 否 | 成交日期开始，格式yyyy-MM-dd | "2025-01-01" |
| payTimeEnd | String | 否 | 成交日期结束，格式yyyy-MM-dd | "2025-01-31" |
| rate | Integer | 否 | 评价星级 | 5 |

## 响应参数

**响应格式：** Excel文件下载

**文件名：** 评价管理列表.xlsx

**Excel列信息：**

| 列名 | 字段说明 | 数据类型 | 示例值 |
|------|----------|----------|--------|
| 序号 | 自动生成的序号 | 数字 | 1 |
| 评价ID | 评价记录的唯一标识 | 数字 | 12345 |
| 订单号 | 关联的订单编号 | 数字 | 202501040001 |
| 用户名 | 评价用户的昵称 | 文本 | "张三" |
| 评价时间 | 评价创建时间 | 日期时间 | "2025-01-04 14:30:00" |
| 评分 | 用户给出的评分 | 数字 | 5 |
| 商品名称 | 评价的商品名称，多个商品用分号分隔 | 文本 | "苹果;香蕉" |
| 评价内容 | 用户的评价文字，多个商品用分号分隔 | 文本 | "很好吃;新鲜" |
| 是否有图片 | 评价是否包含图片，多个商品用分号分隔 | 文本 | "是;否" |
| 是否精选 | 评价是否被设为精选，多个商品用分号分隔 | 文本 | "是;否" |
| 商家回复 | 商家对评价的回复，多个商品用分号分隔 | 文本 | "谢谢;感谢支持" |
| 订单类型 | 订单的类型描述 | 文本 | "商超订单" |

## 请求示例

```http
GET /order-open/manage/evaluate/export?goodsName=苹果&rate=5&payTimeStart=2025-01-01&payTimeEnd=2025-01-31
```

## 响应示例

**成功响应：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": null
}
```

**错误响应：**
```json
{
    "code": 500,
    "message": "导出失败：具体错误信息",
    "data": null
}
```

## 注意事项

1. 导出数据量限制为最大导出条数（通常为10000条）
2. 当一个订单包含多个商品评价时，商品相关字段会用分号（;）分隔显示
3. 时间格式统一为 yyyy-MM-dd HH:mm:ss
4. 布尔值字段统一显示为"是"或"否"
5. 空值字段显示为空字符串

## 相关接口

- **查询评价列表：** `GET /order-open/manage/evaluate/search` - 用于页面展示评价列表
- **设置精选评价：** `PUT /order-open/manage/choice/evaluate` - 设置评价为精选
- **取消精选评价：** `PUT /order-open/manage/unChoice/evaluate` - 取消评价精选状态

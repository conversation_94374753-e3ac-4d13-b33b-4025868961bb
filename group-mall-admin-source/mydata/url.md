# 权益包模块API接口文档

## 目录
- [销售记录] 有合计
- [销售明细](#销售明细)
- [核销单列表](#核销单列表)
- [核销单明细](#核销单明细)
- [权益包汇总](#权益包汇总) 有合计
- [订单管理](#订单管理)
    - [订单明细](#订单明细) 有合计
    - [评价管理](#评价管理)
    - [售后工单](#售后工单)
- [仓库管理](#仓库管理)
    - [商品库存](#商品库存)
    - [商品入库](#商品入库)
    - [入库明细](#入库明细)
- [客户管理](#客户管理)
    - [客户列表](#客户列表)
    - [客户明细](#客户明细)
    - [积分排行](#积分排行)
- [奖励管理](#奖励管理)
    - [奖励明细](#奖励明细)
    - [提成明细](#提成明细)
- [佣金管理](#佣金管理)
    - [提现列表](#提现列表)
- [特惠管理](#特惠管理)
    - [核销列表](#核销列表)
    - [优惠券明细](#优惠券明细)
- [商家管理](#商家管理)
    - [商家信息](#商家信息)
    - [商家申请列表](#商家申请列表)

## 销售明细

### 接口信息
- **接口名称**: pagePackageGoods
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-package-goods/getPageList

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| status | String | 否 | 状态 |
| orderId | String | 否 | 订单号 |

### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| packageName | 权益包名称 | - |
| userName | 用户名称 | - |
| userPhone | 用户电话 | - |
| productName | 商品名称 | - |
| skuName | 规格名称 | - |
| orderId | 订单号 | 点击可跳转到订单详情 |
| departmentName | 购买店面 | - |
| allTimes | 可用次数 | - |
| alreadyTimes | 已用次数 | - |
| 剩余次数 | 剩余次数 | 计算得出：allTimes - alreadyTimes |
| startTime | 开始时间 | - |
| endTime | 结束时间 | - |
| status | 状态 | 100显示为"未用"，101显示为"已用"，200显示为"已失效" |

### 组件路径
- 入口组件: src/views/packages/accountGoodsList/accountGoodsList.vue
- 列表组件: src/views/packages/accountGoodsList/ListApart.vue
- 分页组件: src/views/packages/accountGoodsList/components/pagingList.vue

## 核销单列表

### 接口信息
- **接口名称**: pageUserPackageGoods
- **请求方式**: POST
- **接口路径**: /account-open/mini-platform/packageGoods/pageUserPackageGoods

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |

### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| verifyNo | 核销单号 | - |
| userName | 会员名称 | - |
| packageName | 权益包名称 | - |
| productName | 核销内容 | - |
| 核销数量 | 核销数量 | 固定显示为1 |
| goodsNum | 未核销数量 | - |
| storeFrontName | 核销门店 | - |
| verifyTime | 核销时间 | - |
| accountName | 经手人 | - |

### 组件路径
- 入口组件: src/views/packages/writeOffList/writeOffList.vue
- 列表组件: src/views/packages/writeOffList/ListApart.vue
- 分页组件: src/views/packages/writeOffList/components/pagingList.vue

## 核销单明细

### 接口信息
- **接口名称**: pagePackageGoodsCodeDetail
- **请求方式**: POST
- **接口路径**: /account-open/mini-platform/packageGoods/pagePackageGoodsCodeDetail

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |

### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| packageName | 权益包名称 | - |
| productName | 商品名称 | - |
| verifyGoodsName | 实际核销商品 | - |
| skuName | 商品规格 | - |
| verifyTime | 核销时间 | - |
| verifyNumber | 核销数量 | - |
| canTimes | 剩余数量 | - |
| storeFrontName | 核销门店 | - |
| accountName | 经手人 | - |
| packageShowTime | 权益包展示时间 | 计算得出：packageShowStartTime.substring(0,10) + "~" + packageShowEndTime.substring(0,10) |
| packageTime | 使用期限 | 计算得出：packageStartTime.substring(0,10) + "~" + packageEndTime.substring(0,10) |
| mutexFlag | 互斥商品 | 0显示为"否"，1显示为"是" |
| notTime | 无期限 | 0显示为"否"，1显示为"是" |

### 组件路径
- 入口组件: src/views/packages/writeOffDetailList/writeOffDetailList.vue
- 列表组件: src/views/packages/writeOffDetailList/ListApart.vue
- 分页组件: src/views/packages/writeOffDetailList/components/pagingList.vue

## 权益包汇总

### 接口信息
- **接口名称**: managePackageStatic
- **请求方式**: GET
- **接口路径**: /order-open/manage/managePackageStatic

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| packageName | String | 否 | 权益包名称 |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |
| asc | Number | 否 | 排序方式(0:升序,1:降序) |
| orderBy | Number | 否 | 排序字段(0:销售权益包数量,1:权益包金额) |

### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| name | 权益包名称 | - |
| packageQty | 销售权益包数量 | 支持排序 |
| packageAmount | 权益包金额 | 支持排序 |
| packageVerifyQty | 核销数 | - |
| packageUnVerifyQty | 未核销数 | - |
| 合计行 | 合计 | 通过getSummaries方法计算，对每列数据进行求和 |

### 合计接口
- **接口名称**: managePackageStaticTotal
- **请求方式**: GET
- **接口路径**: /order-open/manage/managePackageStaticTotal

### 合计请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| packageName | String | 否 | 权益包名称 |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |

### 组件路径
- 入口组件: src/views/packages/packageCollect/packageCollect.vue

## 订单管理

### 订单明细

#### 接口信息
- **接口名称**: getOrderDetailByOrderId
- **请求方式**: POST
- **接口路径**: /order-open/manage/searchOrderDetail

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| productName | String | 否 | 商品名称 |
| orderId | String | 否 | 订单号 |
| orderStatus | String | 否 | 订单状态 |
| storeFrontName | String | 否 | 门店名称 |
| startDate | String | 否 | 开始日期 |
| endDate | String | 否 | 结束日期 |
| accountName | String | 否 | 职员名称 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| orderId | 订单号 | 点击可跳转到订单详情 |
| productName | 商品名称 | - |
| productPrice | 单价 | - |
| productQuantity | 数量 | - |
| realAmount | 金额 | - |
| storeFrontName | 门店名称 | - |
| nikeName | 客户名称 | - |
| phone | 电话号码 | - |
| accountName | 职员名称 | - |
| deliverTypeEnumList | 发货方式 | 通过getDeliveryTypeName方法转换 |
| warehouseName | 发货仓库 | - |
| 合计行 | 合计 | 通过getSummaries方法计算，对productQuantity和realAmount列进行求和 |

#### 组件路径
- 入口组件: src/views/order/orderDetails.vue

### 评价管理

#### 接口信息
- **接口名称**: getEvaluateList
- **请求方式**: GET
- **接口路径**: /order-open/manage/evaluate/search

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| choice | Number | 否 | 是否精选 |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| orderId | 订单号 | - |
| createTime | 评价时间 | - |
| userName | 评价人 | - |
| userAvatarUrl | 用户头像 | - |
| itemList.productName | 产品信息 | - |
| itemList.rate | 商品评分 | 以星星图标展示 |
| itemList.comment | 评分内容 | - |
| itemList.picture | 评价图片 | 多张图片以逗号分隔 |
| itemList.reply | 商家回复 | - |
| itemList.choice | 是否精选 | - |

#### 组件路径
- 入口组件: src/views/order/Evaluation.vue

### 售后工单

#### 接口信息
- **接口名称**: getAfterList
- **请求方式**: GET
- **接口路径**: /order-open/after/search

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| orderStatus | String | 否 | 订单状态 |
| area | String | 否 | 区域 |
| assName | String | 否 | 售后名称 |
| deliverType | String | 否 | 配送类型 |
| endTime | String | 否 | 结束时间 |
| startTime | String | 否 | 开始时间 |
| keyword | String | 否 | 关键字 |
| lineId | String | 否 | 线路ID |
| orderId | String | 否 | 订单ID |
| pointName | String | 否 | 点位名称 |
| productName | String | 否 | 商品名称 |
| receiverName | String | 否 | 收货人名称 |
| status | String | 否 | 状态 |
| quicklyDate | String | 否 | 快速日期 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| id | ID | - |
| orderId | 订单号 | - |
| afterType | 售后类型 | - |
| status | 状态 | -1：所有订单, 0：待处理, 1：处理中, 2：已完成, 3：已关闭 |
| createTime | 申请时间 | - |
| productName | 商品名称 | - |
| productQuantity | 数量 | - |
| productAmount | 金额 | - |
| reason | 原因 | - |
| remark | 备注 | - |
| close | 是否关闭 | 通过isClose方法计算 |

#### 组件路径
- 入口组件: src/views/order/AfterSaleOrder.vue

## 仓库管理

### 商品库存

#### 接口信息
- **接口名称**: 未在代码中明确找到
- **请求方式**: POST
- **接口路径**: 未在代码中明确找到

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| storehouseNumber | String | 否 | 商品名称/编码 |
| storehouseName | String | 否 | 商品规格 |
| linkGoodsName | String | 否 | 关联商品名称 |
| specs2 | String | 否 | 商品规格2 |
| optvalue | String | 否 | 库存状态 |
| value | String | 否 | 商品状态 |
| showCategoryId | Array | 否 | 分类ID |
| warehouseDadaList | Array | 否 | 仓库ID |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| goodsCode | 商品编码 | - |
| goodsName | 商品名称 | - |
| specs | 商品规格 | - |
| specs2 | 商品规格2 | - |
| linkGoodsName | 关联商品名称 | - |
| unit | 单位 | - |
| warehouseFullName | 所属仓库 | - |
| stock | 可用库存量 | - |
| upperLimit | 库存上限 | - |
| lowerLimit | 库存下限 | - |

#### 组件路径
- 入口组件: src/views/warehouse/commodityStock/stock.vue

### 商品入库

#### 接口信息
- **接口名称**: GoodsWarehousing
- **请求方式**: GET
- **接口路径**: 未在代码中明确找到

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| OddNumbers | String | 否 | 单号 |
| value1 | Array | 否 | 入库日期范围 |
| id | String | 否 | 仓库ID |
| itemValue | String | 否 | 类型 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| buyNo | 单号 | - |
| buyDate | 入库日期 | - |
| warehouse | 所属仓库 | - |
| buyTypeName | 类型 | - |
| preparerName | 制单人 | - |
| buyQuantity | 入库数量 | - |
| buyAmount | 入库金额 | - |
| remarks | 备注 | - |

#### 组件路径
- 入口组件: src/views/warehouse/Inventory/details.vue

### 入库明细

#### 接口信息
- **接口名称**: queryById
- **请求方式**: GET
- **接口路径**: 未在代码中明确找到

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 是 | 入库单ID |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| productBuyInId | 编号 | - |
| productName | 商品 | - |
| skuSpecs | 规格 | - |
| specs2 | 商品规格2 | - |
| unit | 单位 | - |
| instoreQty | 入库数量 | - |
| instorePrice | 单价 | - |
| instoreAmount | 金额 | - |
| remarks | 备注 | - |
| Outer.warehouse | 仓库 | - |
| Outer.buyNo | 单号 | - |
| Outer.buyDate | 入库日期 | - |
| Outer.preparerName | 制单人 | - |
| Outer.handlerName | 经办人 | - |
| Outer.supplierName | 供应商 | - |
| Outer.buyTypeName | 入库类型 | - |
| Outer.buyAmount | 总入库金额 | - |
| Outer.buyQuantity | 总入库数量 | - |
| Outer.remarks | 备注 | - |

#### 组件路径
- 入口组件: src/views/warehouse/Inventory/detailed.vue

## 客户管理

### 客户列表

#### 接口信息
- **接口名称**: getCustomerList
- **请求方式**: GET
- **接口路径**: /account-open/mini-account/list

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| nikeName | String | 否 | 微信昵称 |
| phone | String | 否 | 手机号 |
| orderSuccessTime | Array | 否 | 上次交易时间范围 |
| tagId | String | 否 | 标签ID |
| sortType | Number | 否 | 排序方式 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| nikeName | 客户昵称 | - |
| phone | 手机号 | - |
| avatarUrl | 头像 | - |
| userTagVos | 标签 | 数组，显示前两个标签名称 |
| oneTeamNum | 一级 | - |
| twoTeamNum | 二级 | - |
| consumeNum | 购次 | - |
| consumeTotleMoney | 交易总额 | - |
| recommendName | 推荐人 | - |
| orderLastDealTime | 上次交易时间 | - |
| currentCommission | 可提现佣金 | - |
| commission | 累计佣金 | - |

#### 组件路径
- 入口组件: src/views/customer/list/Index.vue

### 客户明细

#### 接口信息
- **接口名称**: searchMiniAccountDetail
- **请求方式**: POST
- **接口路径**: /account-open/mini-account/searchMiniAccountDetail

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| nikeName | String | 否 | 客户昵称 |
| phone | String | 否 | 客户手机 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| nikeName | 客户昵称 | - |
| phone | 客户手机 | - |
| memberLevel | 会员等级 | - |
| cardNumber | 会员卡号 | - |
| firstLoginTime | 注册时间 | - |
| integral | 普通积分 | - |
| saleIntegral | 销售积分 | - |
| allIntegral | 总积分 | - |
| usedIntegral | 已用积分 | - |
| currentIntegral | 可用积分 | - |

#### 组件路径
- 入口组件: src/views/customer/detailList/detailList.vue

### 积分排行

#### 接口信息
- **接口名称**: searchIntegralRanking
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-integral/searchIntegralRanking

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| saleIntegralSort | Number | 否 | 销售积分排序 |
| integralSort | Number | 否 | 普通积分排序 |
| allIntegralSort | Number | 否 | 总积分排序 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| nikeName | 客户昵称 | - |
| phone | 客户号码 | - |
| saleIntegral | 销售积分 | - |
| integral | 普通积分 | - |
| allIntegral | 客户总积分 | - |

#### 组件路径
- 入口组件: src/views/customer/integralRanking/Index.vue
- 列表组件: src/views/customer/integralRanking/components/IntegralRanking.vue

## 奖励管理

### 奖励明细

#### 接口信息
- **接口名称**: pageRewardSchemeDet
- **请求方式**: POST
- **接口路径**: /goods-open/reward-scheme/pageRewardSchemeDet

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| billDateSort | Number | 否 | 单据日期排序 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| billNo | 单据编号 | - |
| billDate | 单据日期 | - |
| startTime | 有效期 | 显示格式：startTime~endTime |
| name | 方案名称 | - |
| userName | 经手人 | - |
| createUserName | 制单人 | - |
| remark | 主表备注 | - |
| rewardType | 奖励类型 | 1显示为"佣金"，2显示为"提成" |
| productName | 商品名称 | - |
| priceType | 价格类型 | 1显示为"会员价"，2显示为"复购价"，3显示为"实售价" |
| memberLevelName | 会员等级 | - |
| detRemark | 明细备注 | - |

#### 组件路径
- **组件路径**: @/views/reward/rewardSchemeDet/Index.vue

### 提成明细

#### 接口信息
- **接口名称**: searchMiniAccountRoyaltyDet
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-commission/searchMiniAccountRoyaltyDet

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| nikeName | String | 否 | 会员昵称 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| orderId | 订单编号 | - |
| payTime | 订单时间 | - |
| nikeName | 会员昵称 | - |
| phone | 会员电话号码 | - |
| amount | 提成 | - |
| payAmount | 订单金额 | - |
| remark | 备注 | - |

#### 组件路径
- **组件路径**: @/views/reward/rewardRoyatlyDet/Index.vue

## 佣金管理

### 提现列表

#### 接口信息
- **接口名称**: getMiniCommissionList
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-commission/getMiniAccountCommissionCashVo

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| status | Number | 否 | 状态(0:审核中,1:审核通过,2:提现成功,-1:已驳回,-2:提现失败) |
| createTimeSort | Number | 否 | 申请时间排序 |
| amountSort | Number | 否 | 金额排序 |
| payTimeSort | Number | 否 | 放款时间排序 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| id | 提现单号 | - |
| nikeName | 提现人 | - |
| phone | 提现人电话 | 显示在提现人下方 |
| amount | 提现金额 | - |
| createTime | 申请时间 | - |
| payTime | 放款时间 | 仅在status为300时显示 |
| status | 单据状态 | 0显示为"审核中"，1显示为"审核通过"，-1显示为"已驳回"，-2显示为"提现失败"，2显示为"提现成功" |
| payType | 付款方式 | 102显示为"微信支付" |
| offlinePayType | 付款方式 | 300显示为"银行卡"，301显示为"微信收款码"，302显示为"支付宝收款码" |
| bankUserName | 开户人 | 仅在offlinePayType为300时显示 |
| bankName | 转款银行 | 仅在offlinePayType为300时显示 |
| bankNo | 银行卡号 | 仅在offlinePayType为300时显示 |

#### 组件路径
- **组件路径**: @/views/commission/list/withdrawalList.vue

## 特惠管理

### 核销列表

#### 接口信息
- **接口名称**: pageShopVerifyCode
- **请求方式**: POST
- **接口路径**: /shops-open/shop-pass-ticket/pageShopVerifyCode

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| userMobile | 会员手机号 | - |
| nickName | 会员姓名 | - |
| passTicketName | 通惠证名称 | - |
| verifyCode | 核销码 | - |
| verifyUserMobile | 商家核销号 | - |
| verifyTime | 核销时间 | - |

#### 组件路径
- **组件路径**: @/views/certificate/writeOffList/writeOffList.vue

### 优惠券明细

#### 接口信息
- **接口名称**: getMiniAccountCouponSearchVo
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-coupon/getMiniAccountCouponSearchVo

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| endTimeSort | Number | 否 | 结束时间排序 |
| priceSort | Number | 否 | 价格排序 |
| startTimeSort | Number | 否 | 开始时间排序 |
| useableTimesSort | Number | 否 | 可用次数排序 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| couponName | 优惠券名称 | - |
| promotion | 优惠券面额 | - |
| useName | 使用人 | - |
| verifyNickName | 核销人 | - |
| useShopName | 使用店面 | - |
| orderId | 订单号 | 点击可跳转到订单详情 |
| useTime | 使用时间 | - |
| verifyTime | 核销时间 | - |

#### 组件路径
- **组件路径**: @/views/certificate/accountCouponList/accountCouponList.vue

## 商家管理

### 商家信息

#### 接口信息
- **接口名称**: getShopList
- **请求方式**: POST
- **接口路径**: /shops-open/shops_partner/pageList

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| prohibitStatus | String | 否 | 禁用状态(0:正常,1:禁用) |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| name | 商家名称 | - |
| contacts | 联系人 | - |
| phone | 联系人电话 | - |
| categoryName | 所属分类 | - |
| createTime | 加入时间 | - |
| mainFlag | 主店铺 | 1显示为"是"，其他显示为"否" |
| prohibitStatus | 状态 | 0显示为"启用"，1显示为"停用" |

#### 组件路径
- **组件路径**: @/views/store/storeList/storeList.vue

### 商家申请列表

#### 接口信息
- **接口名称**: getShopsSettled
- **请求方式**: POST
- **接口路径**: /shops-open/shops_settled/getShopsSettledVo

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| approvalStatus | String | 否 | 审批状态(0:临时保存,100:待审核,101:审核通过,200:驳回) |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| shopName | 商家名称 | - |
| contacts | 联系人 | - |
| phone | 联系人电话 | - |
| applyTime | 申请时间 | - |
| approvalStatus | 状态 | 0显示为"临时保存"，100显示为"未审核"，101显示为"已审核"，200显示为"驳回" |

#### 组件路径
- **组件路径**: @/views/store/entryList/entryList.vue **# 权益包模块API接口文档

## 目录
- [销售明细](#销售明细)
- [核销单列表](#核销单列表)
- [核销单明细](#核销单明细)
- [权益包汇总](#权益包汇总)
- [订单管理](#订单管理)
    - [订单明细](#订单明细)
    - [评价管理](#评价管理)
    - [售后工单](#售后工单)
- [仓库管理](#仓库管理)
    - [商品库存](#商品库存)
    - [商品入库](#商品入库)
    - [入库明细](#入库明细)
- [客户管理](#客户管理)
    - [客户列表](#客户列表)
    - [客户明细](#客户明细)
    - [积分排行](#积分排行)
- [奖励管理](#奖励管理)
    - [奖励明细](#奖励明细)
    - [提成明细](#提成明细)
- [佣金管理](#佣金管理)
    - [提现列表](#提现列表)
- [特惠管理](#特惠管理)
    - [核销列表](#核销列表)
    - [优惠券明细](#优惠券明细)
- [商家管理](#商家管理)
    - [商家信息](#商家信息)
    - [商家申请列表](#商家申请列表)

## 销售明细

### 接口信息
- **接口名称**: pagePackageGoods
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-package-goods/getPageList

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| status | String | 否 | 状态 |
| orderId | String | 否 | 订单号 |

### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| packageName | 权益包名称 | - |
| userName | 用户名称 | - |
| userPhone | 用户电话 | - |
| productName | 商品名称 | - |
| skuName | 规格名称 | - |
| orderId | 订单号 | 点击可跳转到订单详情 |
| departmentName | 购买店面 | - |
| allTimes | 可用次数 | - |
| alreadyTimes | 已用次数 | - |
| 剩余次数 | 剩余次数 | 计算得出：allTimes - alreadyTimes |
| startTime | 开始时间 | - |
| endTime | 结束时间 | - |
| status | 状态 | 100显示为"未用"，101显示为"已用"，200显示为"已失效" |

### 组件路径
- 入口组件: src/views/packages/accountGoodsList/accountGoodsList.vue
- 列表组件: src/views/packages/accountGoodsList/ListApart.vue
- 分页组件: src/views/packages/accountGoodsList/components/pagingList.vue

## 核销单列表

### 接口信息
- **接口名称**: pageUserPackageGoods
- **请求方式**: POST
- **接口路径**: /account-open/mini-platform/packageGoods/pageUserPackageGoods

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |

### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| verifyNo | 核销单号 | - |
| userName | 会员名称 | - |
| packageName | 权益包名称 | - |
| productName | 核销内容 | - |
| 核销数量 | 核销数量 | 固定显示为1 |
| goodsNum | 未核销数量 | - |
| storeFrontName | 核销门店 | - |
| verifyTime | 核销时间 | - |
| accountName | 经手人 | - |

### 组件路径
- 入口组件: src/views/packages/writeOffList/writeOffList.vue
- 列表组件: src/views/packages/writeOffList/ListApart.vue
- 分页组件: src/views/packages/writeOffList/components/pagingList.vue

## 核销单明细

### 接口信息
- **接口名称**: pagePackageGoodsCodeDetail
- **请求方式**: POST
- **接口路径**: /account-open/mini-platform/packageGoods/pagePackageGoodsCodeDetail

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |

### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| packageName | 权益包名称 | - |
| productName | 商品名称 | - |
| verifyGoodsName | 实际核销商品 | - |
| skuName | 商品规格 | - |
| verifyTime | 核销时间 | - |
| verifyNumber | 核销数量 | - |
| canTimes | 剩余数量 | - |
| storeFrontName | 核销门店 | - |
| accountName | 经手人 | - |
| packageShowTime | 权益包展示时间 | 计算得出：packageShowStartTime.substring(0,10) + "~" + packageShowEndTime.substring(0,10) |
| packageTime | 使用期限 | 计算得出：packageStartTime.substring(0,10) + "~" + packageEndTime.substring(0,10) |
| mutexFlag | 互斥商品 | 0显示为"否"，1显示为"是" |
| notTime | 无期限 | 0显示为"否"，1显示为"是" |

### 组件路径
- 入口组件: src/views/packages/writeOffDetailList/writeOffDetailList.vue
- 列表组件: src/views/packages/writeOffDetailList/ListApart.vue
- 分页组件: src/views/packages/writeOffDetailList/components/pagingList.vue

## 权益包汇总

### 接口信息
- **接口名称**: managePackageStatic
- **请求方式**: GET
- **接口路径**: /order-open/manage/managePackageStatic

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| packageName | String | 否 | 权益包名称 |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |
| asc | Number | 否 | 排序方式(0:升序,1:降序) |
| orderBy | Number | 否 | 排序字段(0:销售权益包数量,1:权益包金额) |

### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| name | 权益包名称 | - |
| packageQty | 销售权益包数量 | 支持排序 |
| packageAmount | 权益包金额 | 支持排序 |
| packageVerifyQty | 核销数 | - |
| packageUnVerifyQty | 未核销数 | - |
| 合计行 | 合计 | 通过getSummaries方法计算，对每列数据进行求和 |

### 合计接口
- **接口名称**: managePackageStaticTotal
- **请求方式**: GET
- **接口路径**: /order-open/manage/managePackageStaticTotal

### 合计请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| packageName | String | 否 | 权益包名称 |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |

### 组件路径
- 入口组件: src/views/packages/packageCollect/packageCollect.vue

## 订单管理

### 订单明细

#### 接口信息
- **接口名称**: getOrderDetailByOrderId
- **请求方式**: POST
- **接口路径**: /order-open/manage/searchOrderDetail

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| productName | String | 否 | 商品名称 |
| orderId | String | 否 | 订单号 |
| orderStatus | String | 否 | 订单状态 |
| storeFrontName | String | 否 | 门店名称 |
| startDate | String | 否 | 开始日期 |
| endDate | String | 否 | 结束日期 |
| accountName | String | 否 | 职员名称 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| orderId | 订单号 | 点击可跳转到订单详情 |
| productName | 商品名称 | - |
| productPrice | 单价 | - |
| productQuantity | 数量 | - |
| realAmount | 金额 | - |
| storeFrontName | 门店名称 | - |
| nikeName | 客户名称 | - |
| phone | 电话号码 | - |
| accountName | 职员名称 | - |
| deliverTypeEnumList | 发货方式 | 通过getDeliveryTypeName方法转换 |
| warehouseName | 发货仓库 | - |
| 合计行 | 合计 | 通过getSummaries方法计算，对productQuantity和realAmount列进行求和 |

#### 组件路径
- 入口组件: src/views/order/orderDetails.vue

### 评价管理

#### 接口信息
- **接口名称**: getEvaluateList
- **请求方式**: GET
- **接口路径**: /order-open/manage/evaluate/search

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| choice | Number | 否 | 是否精选 |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| orderId | 订单号 | - |
| createTime | 评价时间 | - |
| userName | 评价人 | - |
| userAvatarUrl | 用户头像 | - |
| itemList.productName | 产品信息 | - |
| itemList.rate | 商品评分 | 以星星图标展示 |
| itemList.comment | 评分内容 | - |
| itemList.picture | 评价图片 | 多张图片以逗号分隔 |
| itemList.reply | 商家回复 | - |
| itemList.choice | 是否精选 | - |

#### 组件路径
- 入口组件: src/views/order/Evaluation.vue

### 售后工单

#### 接口信息
- **接口名称**: getAfterList
- **请求方式**: GET
- **接口路径**: /order-open/after/search

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| orderStatus | String | 否 | 订单状态 |
| area | String | 否 | 区域 |
| assName | String | 否 | 售后名称 |
| deliverType | String | 否 | 配送类型 |
| endTime | String | 否 | 结束时间 |
| startTime | String | 否 | 开始时间 |
| keyword | String | 否 | 关键字 |
| lineId | String | 否 | 线路ID |
| orderId | String | 否 | 订单ID |
| pointName | String | 否 | 点位名称 |
| productName | String | 否 | 商品名称 |
| receiverName | String | 否 | 收货人名称 |
| status | String | 否 | 状态 |
| quicklyDate | String | 否 | 快速日期 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| id | ID | - |
| orderId | 订单号 | - |
| afterType | 售后类型 | - |
| status | 状态 | -1：所有订单, 0：待处理, 1：处理中, 2：已完成, 3：已关闭 |
| createTime | 申请时间 | - |
| productName | 商品名称 | - |
| productQuantity | 数量 | - |
| productAmount | 金额 | - |
| reason | 原因 | - |
| remark | 备注 | - |
| close | 是否关闭 | 通过isClose方法计算 |

#### 组件路径
- 入口组件: src/views/order/AfterSaleOrder.vue

## 仓库管理

### 商品库存

#### 接口信息
- **接口名称**: 未在代码中明确找到
- **请求方式**: POST
- **接口路径**: 未在代码中明确找到

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| storehouseNumber | String | 否 | 商品名称/编码 |
| storehouseName | String | 否 | 商品规格 |
| linkGoodsName | String | 否 | 关联商品名称 |
| specs2 | String | 否 | 商品规格2 |
| optvalue | String | 否 | 库存状态 |
| value | String | 否 | 商品状态 |
| showCategoryId | Array | 否 | 分类ID |
| warehouseDadaList | Array | 否 | 仓库ID |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| goodsCode | 商品编码 | - |
| goodsName | 商品名称 | - |
| specs | 商品规格 | - |
| specs2 | 商品规格2 | - |
| linkGoodsName | 关联商品名称 | - |
| unit | 单位 | - |
| warehouseFullName | 所属仓库 | - |
| stock | 可用库存量 | - |
| upperLimit | 库存上限 | - |
| lowerLimit | 库存下限 | - |

#### 组件路径
- 入口组件: src/views/warehouse/commodityStock/stock.vue

### 商品入库

#### 接口信息
- **接口名称**: GoodsWarehousing
- **请求方式**: GET
- **接口路径**: 未在代码中明确找到

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| OddNumbers | String | 否 | 单号 |
| value1 | Array | 否 | 入库日期范围 |
| id | String | 否 | 仓库ID |
| itemValue | String | 否 | 类型 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| buyNo | 单号 | - |
| buyDate | 入库日期 | - |
| warehouse | 所属仓库 | - |
| buyTypeName | 类型 | - |
| preparerName | 制单人 | - |
| buyQuantity | 入库数量 | - |
| buyAmount | 入库金额 | - |
| remarks | 备注 | - |

#### 组件路径
- 入口组件: src/views/warehouse/Inventory/details.vue

### 入库明细

#### 接口信息
- **接口名称**: queryById
- **请求方式**: GET
- **接口路径**: 未在代码中明确找到

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 是 | 入库单ID |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| productBuyInId | 编号 | - |
| productName | 商品 | - |
| skuSpecs | 规格 | - |
| specs2 | 商品规格2 | - |
| unit | 单位 | - |
| instoreQty | 入库数量 | - |
| instorePrice | 单价 | - |
| instoreAmount | 金额 | - |
| remarks | 备注 | - |
| Outer.warehouse | 仓库 | - |
| Outer.buyNo | 单号 | - |
| Outer.buyDate | 入库日期 | - |
| Outer.preparerName | 制单人 | - |
| Outer.handlerName | 经办人 | - |
| Outer.supplierName | 供应商 | - |
| Outer.buyTypeName | 入库类型 | - |
| Outer.buyAmount | 总入库金额 | - |
| Outer.buyQuantity | 总入库数量 | - |
| Outer.remarks | 备注 | - |

#### 组件路径
- 入口组件: src/views/warehouse/Inventory/detailed.vue

## 客户管理

### 客户列表

#### 接口信息
- **接口名称**: getCustomerList
- **请求方式**: GET
- **接口路径**: /account-open/mini-account/list

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| nikeName | String | 否 | 微信昵称 |
| phone | String | 否 | 手机号 |
| orderSuccessTime | Array | 否 | 上次交易时间范围 |
| tagId | String | 否 | 标签ID |
| sortType | Number | 否 | 排序方式 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| nikeName | 客户昵称 | - |
| phone | 手机号 | - |
| avatarUrl | 头像 | - |
| userTagVos | 标签 | 数组，显示前两个标签名称 |
| oneTeamNum | 一级 | - |
| twoTeamNum | 二级 | - |
| consumeNum | 购次 | - |
| consumeTotleMoney | 交易总额 | - |
| recommendName | 推荐人 | - |
| orderLastDealTime | 上次交易时间 | - |
| currentCommission | 可提现佣金 | - |
| commission | 累计佣金 | - |

#### 组件路径
- 入口组件: src/views/customer/list/Index.vue

### 客户明细

#### 接口信息
- **接口名称**: searchMiniAccountDetail
- **请求方式**: POST
- **接口路径**: /account-open/mini-account/searchMiniAccountDetail

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| nikeName | String | 否 | 客户昵称 |
| phone | String | 否 | 客户手机 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| nikeName | 客户昵称 | - |
| phone | 客户手机 | - |
| memberLevel | 会员等级 | - |
| cardNumber | 会员卡号 | - |
| firstLoginTime | 注册时间 | - |
| integral | 普通积分 | - |
| saleIntegral | 销售积分 | - |
| allIntegral | 总积分 | - |
| usedIntegral | 已用积分 | - |
| currentIntegral | 可用积分 | - |

#### 组件路径
- 入口组件: src/views/customer/detailList/detailList.vue

### 积分排行

#### 接口信息
- **接口名称**: searchIntegralRanking
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-integral/searchIntegralRanking

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| saleIntegralSort | Number | 否 | 销售积分排序 |
| integralSort | Number | 否 | 普通积分排序 |
| allIntegralSort | Number | 否 | 总积分排序 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| nikeName | 客户昵称 | - |
| phone | 客户号码 | - |
| saleIntegral | 销售积分 | - |
| integral | 普通积分 | - |
| allIntegral | 客户总积分 | - |

#### 组件路径
- 入口组件: src/views/customer/integralRanking/Index.vue
- 列表组件: src/views/customer/integralRanking/components/IntegralRanking.vue

## 奖励管理

### 奖励明细

#### 接口信息
- **接口名称**: pageRewardSchemeDet
- **请求方式**: POST
- **接口路径**: /goods-open/reward-scheme/pageRewardSchemeDet

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| billDateSort | Number | 否 | 单据日期排序 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| billNo | 单据编号 | - |
| billDate | 单据日期 | - |
| startTime | 有效期 | 显示格式：startTime~endTime |
| name | 方案名称 | - |
| userName | 经手人 | - |
| createUserName | 制单人 | - |
| remark | 主表备注 | - |
| rewardType | 奖励类型 | 1显示为"佣金"，2显示为"提成" |
| productName | 商品名称 | - |
| priceType | 价格类型 | 1显示为"会员价"，2显示为"复购价"，3显示为"实售价" |
| memberLevelName | 会员等级 | - |
| detRemark | 明细备注 | - |

#### 组件路径
- **组件路径**: @/views/reward/rewardSchemeDet/Index.vue

### 提成明细

#### 接口信息
- **接口名称**: searchMiniAccountRoyaltyDet
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-commission/searchMiniAccountRoyaltyDet

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| nikeName | String | 否 | 会员昵称 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| orderId | 订单编号 | - |
| payTime | 订单时间 | - |
| nikeName | 会员昵称 | - |
| phone | 会员电话号码 | - |
| amount | 提成 | - |
| payAmount | 订单金额 | - |
| remark | 备注 | - |

#### 组件路径
- **组件路径**: @/views/reward/rewardRoyatlyDet/Index.vue

## 佣金管理

### 提现列表

#### 接口信息
- **接口名称**: getMiniCommissionList
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-commission/getMiniAccountCommissionCashVo

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| status | Number | 否 | 状态(0:审核中,1:审核通过,2:提现成功,-1:已驳回,-2:提现失败) |
| createTimeSort | Number | 否 | 申请时间排序 |
| amountSort | Number | 否 | 金额排序 |
| payTimeSort | Number | 否 | 放款时间排序 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| id | 提现单号 | - |
| nikeName | 提现人 | - |
| phone | 提现人电话 | 显示在提现人下方 |
| amount | 提现金额 | - |
| createTime | 申请时间 | - |
| payTime | 放款时间 | 仅在status为300时显示 |
| status | 单据状态 | 0显示为"审核中"，1显示为"审核通过"，-1显示为"已驳回"，-2显示为"提现失败"，2显示为"提现成功" |
| payType | 付款方式 | 102显示为"微信支付" |
| offlinePayType | 付款方式 | 300显示为"银行卡"，301显示为"微信收款码"，302显示为"支付宝收款码" |
| bankUserName | 开户人 | 仅在offlinePayType为300时显示 |
| bankName | 转款银行 | 仅在offlinePayType为300时显示 |
| bankNo | 银行卡号 | 仅在offlinePayType为300时显示 |

#### 组件路径
- **组件路径**: @/views/commission/list/withdrawalList.vue

## 特惠管理

### 核销列表

#### 接口信息
- **接口名称**: pageShopVerifyCode
- **请求方式**: POST
- **接口路径**: /shops-open/shop-pass-ticket/pageShopVerifyCode

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| userMobile | 会员手机号 | - |
| nickName | 会员姓名 | - |
| passTicketName | 通惠证名称 | - |
| verifyCode | 核销码 | - |
| verifyUserMobile | 商家核销号 | - |
| verifyTime | 核销时间 | - |

#### 组件路径
- **组件路径**: @/views/certificate/writeOffList/writeOffList.vue

### 优惠券明细

#### 接口信息
- **接口名称**: getMiniAccountCouponSearchVo
- **请求方式**: POST
- **接口路径**: /account-open/mini-account-coupon/getMiniAccountCouponSearchVo

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| endTimeSort | Number | 否 | 结束时间排序 |
| priceSort | Number | 否 | 价格排序 |
| startTimeSort | Number | 否 | 开始时间排序 |
| useableTimesSort | Number | 否 | 可用次数排序 |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| couponName | 优惠券名称 | - |
| promotion | 优惠券面额 | - |
| useName | 使用人 | - |
| verifyNickName | 核销人 | - |
| useShopName | 使用店面 | - |
| orderId | 订单号 | 点击可跳转到订单详情 |
| useTime | 使用时间 | - |
| verifyTime | 核销时间 | - |

#### 组件路径
- **组件路径**: @/views/certificate/accountCouponList/accountCouponList.vue

## 商家管理

### 商家信息

#### 接口信息
- **接口名称**: getShopList
- **请求方式**: POST
- **接口路径**: /shops-open/shops_partner/pageList

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| prohibitStatus | String | 否 | 禁用状态(0:正常,1:禁用) |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| name | 商家名称 | - |
| contacts | 联系人 | - |
| phone | 联系人电话 | - |
| categoryName | 所属分类 | - |
| createTime | 加入时间 | - |
| mainFlag | 主店铺 | 1显示为"是"，其他显示为"否" |
| prohibitStatus | 状态 | 0显示为"启用"，1显示为"停用" |

#### 组件路径
- **组件路径**: @/views/store/storeList/storeList.vue

### 商家申请列表

#### 接口信息
- **接口名称**: getShopsSettled
- **请求方式**: POST
- **接口路径**: /shops-open/shops_settled/getShopsSettledVo

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| current | Number | 是 | 页码 |
| size | Number | 是 | 每页条数 |
| approvalStatus | String | 否 | 审批状态(0:临时保存,100:待审核,101:审核通过,200:驳回) |

#### 显示字段
| 字段名 | 显示名称 | 描述 |
| ------ | -------- | ---- |
| shopName | 商家名称 | - |
| contacts | 联系人 | - |
| phone | 联系人电话 | - |
| applyTime | 申请时间 | - |
| approvalStatus | 状态 | 0显示为"临时保存"，100显示为"未审核"，101显示为"已审核"，200显示为"驳回" |

#### 组件路径
- **组件路径**: @/views/store/entryList/entryList.vue **
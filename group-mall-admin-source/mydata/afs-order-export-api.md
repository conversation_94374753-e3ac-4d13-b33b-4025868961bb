# 售后工单导出API文档

## 接口信息

**接口名称**: 商家导出售后工单列表  
**接口地址**: `GET /afs-open/manage/export`  
**接口描述**: 根据查询条件导出售后工单列表到Excel文件  

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| goodsName | String | 否 | 商品名称，模糊搜索 | "苹果" |
| userName | String | 否 | 买家昵称，模糊搜索 | "张三" |
| receiverName | String | 否 | 收货人名称，模糊搜索 | "李四" |
| orderId | String | 否 | 订单编号，精确搜索 | "202501040001" |
| pointName | String | 否 | 取货地点，模糊搜索 | "小区门口" |
| lineId | Long | 否 | 线路id | 1 |
| deliverType | Integer | 是 | 配送方式 0->全部;100->用户自提;102->物流配送 | 0 |
| startTime | String | 否 | 开始日期,格式2019-10-10 | "2025-01-01" |
| endTime | String | 否 | 结束日期,格式2019-10-10 | "2025-01-31" |
| status | Integer | 是 | 订单状态 -1：所有订单, 0.待处理, 1.处理中, 2.已完成, 3.已关闭 | -1 |
| area | Integer | 是 | 销售专区 1->社区；2->商超 | 1 |
| shopIds | List<String> | 否 | 店铺id列表 | ["shop001", "shop002"] |
| queryShopIds | String | 否 | 店铺id字符串 | "shop001,shop002" |
| shopIds2 | List<String> | 否 | 搜索店铺id列表 | ["shop001"] |

## 响应结果

**成功响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

**失败响应**:
```json
{
    "code": 500,
    "msg": "导出失败",
    "data": null
}
```

## 导出字段说明

| 字段名 | 说明 | 数据类型 |
|--------|------|----------|
| 序号 | 自动生成的序号 | 数字 |
| ID | 售后工单的唯一标识 | 数字 |
| 订单号 | 申请售后的订单号 | 数字 |
| 售后类型 | 退款/退货退款 | 文本 |
| 状态 | -1：所有订单, 0：待处理, 1：处理中, 2：已完成, 3：已关闭 | 文本 |
| 申请时间 | 售后工单申请时间 | 日期时间 |
| 商品名称 | 申请售后的商品名称 | 文本 |
| 数量 | 申请售后的商品数量 | 数字 |
| 金额 | 售后退款金额 | 金额 |
| 原因 | 申请售后的原因 | 文本 |
| 备注 | 售后工单备注信息 | 文本 |
| 是否关闭 | 通过isClose方法计算，是/否 | 文本 |

## 注意事项

1. 导出数据量受系统限制，最大导出数量为系统配置的最大值
2. 导出文件格式为Excel(.xlsx)
3. 导出文件名格式为"售后工单列表_导出时间.xlsx"
4. 所有枚举类型字段都会转换为对应的中文描述
5. 时间字段格式为"yyyy-MM-dd HH:mm:ss"
6. "是否关闭"字段通过AfsOrderStatusEnum的isEnd()方法计算

## 调用示例

```bash
# 导出所有售后工单
GET /afs-open/manage/export?deliverType=0&status=-1&area=1

# 按时间范围导出
GET /afs-open/manage/export?deliverType=0&status=-1&area=1&startTime=2025-01-01&endTime=2025-01-31

# 按商品名称搜索导出
GET /afs-open/manage/export?deliverType=0&status=-1&area=1&goodsName=苹果
```

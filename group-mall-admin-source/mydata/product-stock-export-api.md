# 商品库存导出API文档

## 接口信息

**接口名称**: 商品库存导出  
**接口地址**: `POST /goods-open/manager/productStock/export`  
**接口描述**: 根据查询条件导出商品库存列表到Excel文件  

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| id | Long | 否 | 库存记录ID | 1 |
| warehouseId | Long | 否 | 仓库ID | 1 |
| warehouseIdList | List<Long> | 否 | 仓库ID列表 | [1, 2, 3] |
| skuId | Long | 否 | 规格ID | 1 |
| specs | String | 否 | 商品规格 | "红色/L码" |
| showCategoryIdList | List<Long> | 否 | 商品展示分类ID列表 | [1, 2] |
| keyword | String | 否 | 搜索关键词（商品名称或商品编码） | "苹果" |
| status | Integer | 否 | 商品状态 -1:全部 0:下架 1:上架 | -1 |
| stockStatus | Integer | 否 | 库存状态 1:大于上限 2:小于下限 | 1 |
| stock | BigDecimal | 否 | 库存数量 | 100.00 |
| lowStock | Integer | 否 | 预警库存 | 10 |
| productId | Long | 否 | 商品ID | 1 |
| remark | String | 否 | 备注 | "备注信息" |
| lowerLimit | BigDecimal | 否 | 库存下限 | 10.00 |
| upperLimit | BigDecimal | 否 | 库存上限 | 1000.00 |
| goodsCode | String | 否 | 商品编码 | "GOODS001" |
| stockCode | String | 否 | 仓库标识 | "WH001" |
| specs2 | String | 否 | 商品规格2（通讯行业指颜色） | "红色" |
| linkGoodsName | String | 否 | 关联商品名称 | "关联商品" |

## 响应结果

**成功响应**:
```json
{
    "code": 200,
    "msg": "导出成功！",
    "data": null
}
```

**失败响应**:
```json
{
    "code": 500,
    "msg": "导出失败",
    "data": null
}
```

## 导出字段说明

| 字段名 | 说明 | 数据类型 |
|--------|------|----------|
| 序号 | 自动生成的序号 | 数字 |
| 商品编码 | 商品的唯一编码标识 | 文本 |
| 商品名称 | 商品的名称 | 文本 |
| 商品规格 | 商品的规格信息 | 文本 |
| 商品规格2 | 商品的第二规格信息（通讯行业指颜色） | 文本 |
| 关联商品名称 | 关联商品的名称 | 文本 |
| 单位 | 商品的基本单位 | 文本 |
| 所属仓库 | 商品所在仓库的全称 | 文本 |
| 可用库存量 | 当前可用的库存数量 | 数字 |
| 库存上限 | 库存的上限值 | 数字 |
| 库存下限 | 库存的下限值 | 数字 |

## 注意事项

1. 导出数据量受系统限制，最大导出数量为系统配置的最大值
2. 导出文件格式为Excel(.xlsx)
3. 导出文件名格式为"商品库存列表_导出时间.xlsx"
4. 所有数值字段保持原始精度
5. 库存数量使用BigDecimal类型确保精度

## 调用示例

```bash
# 导出所有商品库存
POST /goods-open/manager/productStock/export
Content-Type: application/json

{
    "status": -1
}

# 按仓库导出
POST /goods-open/manager/productStock/export
Content-Type: application/json

{
    "warehouseId": 1,
    "status": -1
}

# 按关键词搜索导出
POST /goods-open/manager/productStock/export
Content-Type: application/json

{
    "keyword": "苹果",
    "status": -1
}

# 按库存状态导出
POST /goods-open/manager/productStock/export
Content-Type: application/json

{
    "stockStatus": 2,
    "status": -1
}
```

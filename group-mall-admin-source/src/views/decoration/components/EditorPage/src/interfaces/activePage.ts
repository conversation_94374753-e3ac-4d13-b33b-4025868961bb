/*
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-24 09:00:59
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-27 13:58:40
 */
export interface IActivePageItem {
  id: string;
  isDef: string;
  isAgain: number;
  isDeleted: string;
  modelId: string | number | null;
  pageName: string;
  templateId: string;
  type: string;
  operatorId?: string;
  operatorName?: string;
  name?: string;
}

export type IActivePage = IActivePageItem | "usercenter";

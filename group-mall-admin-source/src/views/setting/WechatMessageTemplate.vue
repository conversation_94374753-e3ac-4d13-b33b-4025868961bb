<template>
    <div>
        <m-card class="form" :needToggle="true">
            <el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
                <el-row :gutter="40">
                    <el-col :span="10">
                        <el-form-item label="模板标识">
                            <el-select v-model="dataForm.code" placeholder="请选择模板标识">
                                <el-option v-for="item in dataForm.codeList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="模板名称"><el-input v-model="dataForm.name" clearable
                                placeholder="请输入模板名称" /></el-form-item>
                    </el-col>

                </el-row>
                <el-row :gutter="40">
                    <el-col :span="10">
                        <el-form-item label="模板类型">
                            <el-select v-model="dataForm.optionTypeValue" placeholder="请选择">
                                <el-option v-for="item in dataForm.optionType" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>

                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="模板状态">
                            <el-select v-model="dataForm.optionStatusValue" placeholder="请选择">
                                <el-option v-for="item in dataForm.optionStatus" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-button type="primary" style="margin-left:100px" @click="getTemplateList(1)">搜索</el-button>
            </el-form>
        </m-card>

        <el-button type="primary" v-if="isSupper || addButton" icon="el-icon-plus" style="margin:20px;"
            @click="dialogVisible = true;">添加</el-button>

        <!-- 表格 -->
        <el-table :data="wxTemplateList" border style="width: 100%">
            <el-table-column label="序号" type="index" width="50">
            </el-table-column>
            <el-table-column label="模板标识" width="180">
                <template slot-scope="scope">
                    <div v-if="scope.row.code === '1'">活动通知</div>
                    <div v-if="scope.row.code === '2'">生日祝福提醒</div>
                   
                    <div v-if="scope.row.code === '4'">卖家发货提醒</div>
                </template>
            </el-table-column>
            <el-table-column prop="templateId" label="模板id" width="180">
            </el-table-column>
            <el-table-column prop="name" label="模板名称">
            </el-table-column>
            <el-table-column label="模板类型">
                <template slot-scope="scope">
                    {{ scope.row.type === 1 ? '订阅消息' : '公众号模板消息' }}
                </template>
            </el-table-column>
            <el-table-column label="模板状态">
                <template slot-scope="scope">
                    {{ scope.row.status === 1 ? '启用' : '停用' }}
                </template>
            </el-table-column>
            <el-table-column label="编辑">
                <template slot-scope="scope">
                    <el-button type="text" v-if="isSupper || editButton" @click="editTemplate(scope.row)">编辑</el-button>
                    <el-button type="text" v-if="isSupper || editButton"
                        @click="editStatus(scope.row.id, scope.row.status)">{{ scope.row.status === 1
                            ? '停用' : '启用' }}</el-button>
                    <el-button style="color: red;" v-if="isSupper || deleteButton" type="text"
                        @click="deleteTemplate(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 新增模态框 -->
        <el-dialog title="新增模板" :visible.sync="dialogVisible" width="40%" @close="closeDialog('addForm')">
            <el-form :rules="addRules" ref="addForm" :model="addForm" label-width="100px">
                <el-form-item prop="name" label="模板名称"><el-input v-model="addForm.name" clearable
                    placeholder="请输入模板名称" /></el-form-item>
                <el-form-item prop="templateId" label="模板id"><el-input v-model="addForm.templateId" clearable
                        placeholder="请输入模板id" /></el-form-item>
                <el-form-item prop="code" label="模板标识">
                    <el-select v-model="addForm.code" placeholder="请选择模板标识">
                        <el-option v-for="item in addForm.codeList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="optionTypeValue" label="模板类型">
                    <el-select v-model="addForm.optionTypeValue" placeholder="请选择模板类型">
                        <el-option v-for="item in addForm.optionType" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="模板状态">
                    <el-select v-model="addForm.optionStatus" placeholder="请选择模板状态">
                        <el-option label="启用" value="1"></el-option>
                        <el-option label="禁用" value="2"></el-option>
                    </el-select>
                </el-form-item> -->
            </el-form>
            <el-button icon="el-icon-plus" style="margin-left:30px" type="primary" plain
                @click="addProperties">添加模板属性</el-button>
            <div class="templateProperties">
                <div class="templateProperties-title">
                    <div class="propertiesName">字段名称</div>
                    <div class="propertiesKey">字段标识</div>

                    <div>操作</div>
                </div>
                <div class="templateProperties-info" v-for="(item, index) in templateProperties" :key="index">
                    <div class="propertiesName-ipt"><el-input v-model="item.name"></el-input></div>
                    <div class="propertiesKey-ipt"><el-input v-model="item.keyData"></el-input></div>

                    <div class="deleteTemplateProperties" @click="templateProperties.splice(index, 1)">删除</div>
                </div>
                <div v-show="templateProperties.length === 0" class="empty">暂无数据~</div>
            </div>

            <el-button style="margin-left:30px" type="primary" @click="addTemplate('addForm')">确定</el-button>
        </el-dialog>

        <!-- 编辑模态框 -->
        <el-dialog title="编辑模板" :visible.sync="editDialogVisible" width="40%" @close="templateProperties = []">
            <el-form ref="editFormRef" :model="editForm" label-width="100px">
                <el-form-item label="模板id"><el-input v-model="editForm.templateId" clearable
                        placeholder="请输入模板id" /></el-form-item>
                <el-form-item label="模板标识">
                    <el-select v-model="editForm.code" placeholder="请选择模板标识">
                        <el-option v-for="item in editForm.codeList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="模板名称"><el-input v-model="editForm.name" clearable
                        placeholder="请输入模板名称" /></el-form-item>
                <el-form-item label="模板类型">
                    <el-select v-model="editForm.optionTypeValue" placeholder="请选择模板类型">
                        <el-option v-for="item in editForm.optionType" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <!-- 
                <el-form-item label="模板状态">
                    <el-select v-model="editForm.optionStatusValue" placeholder="请选择模板状态">
                        <el-option v-for="item in editForm.Status" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item> -->
                <el-button icon="el-icon-plus" style="margin-left:30px" type="primary" plain
                    @click="addProperties">添加模板属性</el-button>
                <div class="templateProperties">
                    <div class="templateProperties-title">
                        <div class="propertiesName">字段名称</div>
                        <div class="propertiesKey">字段标识</div>

                        <div>操作</div>
                    </div>
                    <div class="templateProperties-info" v-for="(item, index) in templateProperties" :key="index">
                        <div class="propertiesName-ipt"><el-input v-model="item.name"></el-input></div>
                        <div class="propertiesKey-ipt"><el-input v-model="item.keyData"></el-input></div>

                        <div class="deleteTemplateProperties" @click="templateProperties.splice(index, 1)">删除</div>
                    </div>
                    <div v-show="templateProperties.length === 0" class="empty">暂无数据~</div>
                </div>
            </el-form>
            <el-button style="margin-left:30px" type="primary" @click="upload">确定</el-button>
        </el-dialog>

        <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
            @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>
</template>

<script lang="ts">
import { wxTemplate, wxTemplateEdit, wxTemplateAdd, wxTemplateUpdate, wxTemplateDelete, wxTemplateEnable } from '@/api/wxTemplate/wxTemplate';
import PageManage from '@/components/PageManage.vue';
export default {
    data() {
        return {
            dataForm: {
                code: '',
                codeList: [
                    {
                        value: '',
                        label: '全部'
                    },
                    {
                        value: 1,
                        label: '活动通知'
                    }, {
                        value: 2,
                        label: '生日祝福提醒'
                    },  {
                        value: 4,
                        label: '卖家发货提醒'
                    }
                ],
                name: '',
                optionTypeValue: '',
                optionStatusValue: '',
                optionType: [
                    { value: '', label: '全部' },
                    { value: '1', label: '订阅消息' },
                    { value: '2', label: '公众号模板消息' },
                ],

                optionStatus: [
                    { value: '', label: '全部' },
                    { value: '1', label: '启用' },
                    { value: '2', label: '禁用' },
                ]
            },
            wxTemplateList: [],
            dialogVisible: false,
            editDialogVisible: false,
            addForm: {
                code: '',
                codeList: [
                    {
                        value: 1,
                        label: '活动通知'
                    }, {
                        value: 2,
                        label: '生日祝福提醒'
                    }, {
                        value: 4,
                        label: '卖家发货提醒'
                    }
                ],
                name: '',
                id: '',
                templateId: '',
                optionTypeValue: '',
                optionStatusValue: '',
                optionType: [
                    { value: '1', label: '订阅消息' },
                    { value: '2', label: '公众号模板消息' },
                ],
                optionStatus: [
                    { value: '1', label: '启用' },
                    { value: '2', label: '禁用' }
                ]
            },
            editForm: {
                id: '',
                code: '',
                codeList: [

                    {
                        value: '1',
                        label: '活动通知'
                    }, {
                        value: '2',
                        label: '生日祝福提醒'
                    }, {
                        value: '4',
                        label: '卖家发货提醒'
                    }
                ],
                name: '',
                templateId: '',
                optionTypeValue: '',
                optionStatusValue: '',
                optionType: [
                    { value: 1, label: '订阅消息' },
                    { value: 2, label: '公众号模板消息' },
                ],
                Status: [
                    { value: 1, label: '启用' },
                    { value: 2, label: '禁用' }
                ]
            },
            templateProperties: [] as Array<{ name: string; keyData: string; }>,
            /** 分页条数 */
            pageSize: 10,

            /** 分页页码 */
            pageNum: 1,

            /** 数据长度 */
            total: 0,

            /** 添加表单规则 */
            addRules: {
                code: [
                    { required: true, message: '请选择模板标识', trigger: 'change' }
                ],
                name: [
                    { required: true, message: '请输入模板名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
                ],
                templateId: [
                    { required: true, message: '请输入模板id', trigger: 'blur' },
                    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
                ],
                optionTypeValue: [
                    { required: true, message: '请选择模板类型', trigger: 'change' }
                ],

            },
            menuName: '消息模板',
            buttonList: [],
            isSupper: 0,
            addButtonCode: "wechatMessageTemplate.add",
            addButton: false,
            editButtonCode: "wechatMessageTemplate.edit",
            editButton: false,
            deleteButtonCode: "wechatMessageTemplate.delete",
            deleteButton: false,
        }
    },
    mounted() {
        this.getTemplateList(1);
        this.buttonAuth();
    },

    methods: {

        buttonAuth() {
            this.isSupper = this.$STORE.userStore.userInfo.isSupper
            let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)
            let buttonList = [];
            authMenuButtonVos.forEach(element => {
                buttonList.push(element.buttonCode);
            });
            this.buttonList = buttonList

            var addButtonData = buttonList.find(e => e == this.addButtonCode);
            if (addButtonData != null && addButtonData != undefined) {
                this.addButton = true;
            }

            var editButtonData = buttonList.find(e => e == this.editButtonCode);
            if (editButtonData != null && editButtonData != undefined) {
                this.editButton = true;
            }

            var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);
            if (deleteButtonData != null && deleteButtonData != undefined) {
                this.deleteButton = true;
            }

        },


        /**
        * 
        * 获取模板列表
        */
        getTemplateList(pageNum: number) {
            let dataForm = this.dataForm;
            let params = {
                name: dataForm.name,
                code: dataForm.code,
                type: dataForm.optionTypeValue,
                status: dataForm.optionStatusValue,
                size: this.pageSize,
                current: pageNum
            }
            wxTemplate(params).then((res: any) => {
                this.wxTemplateList = res.data.list;
                this.total = res.data.total;
                this.pageNum = res.data.current;

            })
        },

        /**
         * 
         * 编辑模板
         */
        editTemplate(row: any) {
            this.editDialogVisible = true;
            //获取要编辑的模板信息
            let { code, name, templateId, type, status, id } = row;
            this.editForm = {
                ...this.editForm,
                id: id,
                code: code,
                name: name,
                templateId: templateId,
                optionTypeValue: type,
                optionStatusValue: status,
            }
            //将id传给后台
            wxTemplateEdit({ id }).then((res: any) => {
                this.templateProperties = res.data.list;
            })
        },

        /**
         * 添加
         */
        addTemplate(formName: string) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let addForm = this.addForm;
                    let templateProperties = this.templateProperties;
                    let params = {
                        name: addForm.name,
                        code: addForm.code,
                        type: addForm.optionTypeValue,
                        templateId: addForm.templateId,
                        list: [...templateProperties]
                    }
                    wxTemplateAdd(params).then((res: any) => {
                        this.$message.success('添加成功');
                        this.dialogVisible = false;
                        //置空表单数据
                        this.addForm = {
                            ...this.addForm,
                            code: '',
                            name: '',
                            templateId: '',
                            optionTypeValue: '',
                            optionStatusValue: '',
                        }
                        this.templateProperties = [];
                        this.getTemplateList(1);
                    }).catch((err: any) => {
                        this.$message.error('添加失败');
                    })
                } else {
                    this.$message.error('请检查输入项');
                    return false;
                }
            });

        },
        /**
         * 添加属性
         */
        addProperties() {
            this.templateProperties.push({
                name: '',
                keyData: '',

            });
        },
        /**
         * 编辑更新
         */
        upload() {
            let editForm = this.editForm;
            let templateProperties = this.templateProperties;
            let params = {
                id: editForm.id,
                name: editForm.name,
                code: editForm.code,
                type: editForm.optionTypeValue,
                status: editForm.optionStatusValue,
                templateId: editForm.templateId,
                list: [...templateProperties]
            }
            wxTemplateUpdate(params).then((res: any) => {
                this.$message.success('编辑成功');
                this.editDialogVisible = false;
                //置空表单数据
                this.editForm = {
                    ...this.editForm,
                    code: '',
                    name: '',
                    templateId: '',
                    optionTypeValue: '',
                    optionStatusValue: '',
                }
                this.templateProperties = [];
                this.getTemplateList(1);
            }).catch((err: any) => {
                this.$message.error('编辑失败');
            })
        },

        /**
        * @method handleSizeChange
        * @description 每页 条
        */
        handleSizeChange(val: number) {
            this.pageSize = val;
            this.getTemplateList(1);
        },
        /**
        * @method handleCurrentChange
        * @description 当前页
        */
        handleCurrentChange(val: number) {
            this.pageNum = val;
            this.getTemplateList(val);
        },
        /***
         * 删除模板
         */
        deleteTemplate(row: any) {
            let { id } = row;
            wxTemplateDelete({ id }).then((res: any) => {
                this.$message.success('删除成功');
                this.getTemplateList(1);
            }).catch((err: any) => {
                this.$message.error('删除失败');
            })
        },
        /**
         * 
         * 编辑状态
         */
        editStatus(id: string, status: number) {
            let params = {
                id: id,
                status: status === 1 ? 2 : 1 // 1启用 2禁用
            }
            wxTemplateEnable(params).then(res => {
                this.$message.success(res.data);
                this.getTemplateList(1);
            })
                .catch(err => {
                    this.$message.error("编辑状态失败");
                })
        },

        closeDialog(formName: string) {
            this.templateProperties = []
            this.$refs[formName].resetFields();
        }
    },
    components: {
        PageManage
    }
}


</script>
<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.templateProperties {
    margin-top: 20px;
    width: 90%;
    margin: 20px auto;
    border: 1px solid #d7d7d7;
    padding: 20px;
}

.templateProperties .templateProperties-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.templateProperties-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.deleteTemplateProperties {
    color: red;
    cursor: pointer;
}

.empty {
    color: #d7d7d7;
    text-align: center;
    margin-top: 20px;
}
</style>
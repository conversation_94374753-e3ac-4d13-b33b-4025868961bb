<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:52:07
-->
<template>
  <m-card class="form" :needToggle="true">
    <el-form ref="form" :model="searchType" label-width="90px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="权益包名称">
            <el-input v-model="searchType.packageName" placeholder="请输入权益包名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商品名称">
            <el-input v-model="searchType.productName" placeholder="请输入商品名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="会员名称">
            <el-input v-model="searchType.userName" placeholder="请输入会员名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="门店名称">
            <el-input v-model="searchType.storeFrontName" placeholder="请输入门店名称"></el-input>
          </el-form-item>
        </el-col>

      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="经手人">
            <el-input v-model="searchType.accountName" placeholder="请输入经手人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="核销时间">
            <el-date-picker v-model="value0" type="daterange" range-separator="-" start-placeholder="开始日期"
              end-placeholder="结束日期" style="width: 220px;" @change="chooseTime">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
      </el-form-item>
    </el-form>
  </m-card>
  <!-- </div> -->
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { SearchState, SearchKeyType } from "./searchType";
import DateUtil from "@/store/modules/date";
import { DatePickerOptions } from "element-ui/types/date-picker";
// import { watch } from "vue";

@Component
export default class Searchs extends Vue implements SearchState {
  name = "Searchs";
  @Prop({})
  status!: string;

  pickerOptions: DatePickerOptions = {
    shortcuts: [{
      text: '最近一周',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit('pick', [start, end]);
      }
    }, {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        picker.$emit('pick', [start, end]);
      }
    }, {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
        picker.$emit('pick', [start, end]);
      }
    }]
  };
  couponTypeOption = [{
    value: '',
    label: '全部'
  }, {
    value: 0,
    label: '普通券'
  }, {
    value: 1,
    label: '新人券'
  }, {
    value: 2,
    label: "商品优惠券",
  },
  {
    value: 3,
    label: "品类优惠券",
  }];
  // 审核状态:100->待审核;101->审核通过;200->驳回
  options = [{
    value: '',
    label: '全部'
  }, {
    value: 100,
    label: '未审核'
  }, {
    value: 101,
    label: '已审核'
  }];
  showCateList = [];

  searchType = {
    packageName: '',
    productName: '',
    userName: '',
    storeFrontName: '',
    accountName: '',
    startTime: '',
    endTime: '',
  } as SearchKeyType;
  value0 = ''
  value1 = ''

  created() {
    // var cache = JSON.parse(
    // 	localStorage.getItem("cache_certificateList_search_form") || "{}"
    // );
    //this.searchType = Object.assign(this.searchType, cache) as SearchKeyType;
    this.value0 = [this.searchType.displayStartTime, this.searchType.displayEndTime];
    this.value1 = [this.searchType.startTimeBegin, this.searchType.startTimeEnd];
    // console.log('ffffffffff',this.searchType,cache);
  }
  chooseTime(data: any) {
    this.searchType.startTime = data ? this.dateConversion(data[0]) : "";
    this.searchType.endTime = data ? this.dateConversion(data[1]) : "";
  }
  chooseTimes(data: any) {
    this.searchType.startTimeBegin = data ? this.dateConversion(data[0]) : "";
    this.searchType.startTimeEnd = data ? this.dateConversion(data[1]) : "";
  }

  dateConversion(value: Date) {
    const date = new DateUtil("").getYMDHMSs(value);
    return date;
  }

  search() {
    this.$emit("searchBy", this.searchType);
  }
  exprotData() {
    this.$emit("exprotBy", this.searchType);
  }
}
</script>

<style lang="scss" scoped>
.el-form-item .el-input {
  width: 224px;
}

.el-form-item .el-button {
  width: 90px;
}

@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  &.show {
    height: 240px;
    margin-bottom: 20px;
  }

  &.hide {
    margin-bottom: 20px;
    height: 50px;

    .form__btn {
      width: 940px;
      height: 50px;
      background: #f9f9f9;
      line-height: 50px;
      // margin-top: 20px
    }
  }

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}

.page {
  // height: 270px;
  background-color: #f9f9f9;
  margin-bottom: 20px;
}

@include b(search) {
  display: flex;
  flex-wrap: wrap;

  @include e(item) {
    padding: 20px 40px 10px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include m(text) {
      width: 60px;
    }
  }

  @include e(icon) {
    width: 40px;
    text-align: center;
    border-left: 1px solid #dcdfe6;
    cursor: pointer;
    vertical-align: middle;
  }
}

@include b(searchButton) {
  margin: 20px 30px;
}
</style>

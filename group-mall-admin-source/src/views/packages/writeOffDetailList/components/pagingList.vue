<template>
	<div style="margin-top: 20px;background: red;">
		<el-table :data.sync="ticketList" :selection="true" :checked-item.sync="tableCheckedItem" slot="content"
			class="imgView">
			<el-table-column label="序号" type="index" width="50">
			</el-table-column>
			<el-table-column prop="packageName" label="权益包名称" width="120">
				<template v-slot="{ row }">
					<span>{{ row.packageName }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="productName" label="商品名称" width="120">
				<template v-slot="{ row }">
					<span>{{ row.productName }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="verifyGoodsName" label="实际核销商品" width="120">
				<template v-slot="{ row }">
					<span>{{ row.verifyGoodsName }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="skuName" label="商品规格"  width="120">
				<template v-slot="{ row }">
					<span>{{ row.skuName }}</span>
				</template>
			</el-table-column>

      <el-table-column prop="userName" label="会员名称" width="100">
        <template v-slot="{ row }">
          <span>{{ row.userName }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="userMobile" label="会员号码" width="120">
        <template v-slot="{ row }">
          <span>{{ row.userMobile }}</span>
        </template>
      </el-table-column>

			<el-table-column prop="verifyTime" label="核销时间" width="150">
				<template v-slot="{ row }">
					<span>{{ row.verifyTime }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="verifyNumber" label="核销数量" width="80">
				<template v-slot="{ row }">
					<span>{{ row.verifyNumber }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="canTimes" label="剩余数量" width="80">
				<template v-slot="{ row }">
					<span>{{ row.canTimes }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="storeFrontName" label="核销门店" width="100">
				<template v-slot="{ row }">
					<span>{{ row.storeFrontName}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="accountName" label="经手人" width="100">
				<template v-slot="{ row }">
					<span>{{ row.accountName}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="packageShowTime" label="权益包展示时间" width="150">
				<template v-slot="{ row }">
					<span>{{ row.packageShowStartTime.substring(0,10)}}~{{ row.packageShowEndTime.substring(0,10)}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="packageShowTime" label="使用期限" width="150">
				<template v-slot="{ row }">
					<span>{{ row.packageStartTime.substring(0,10)}}~{{ row.packageEndTime.substring(0,10)}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="mutexFlag" label="互斥商品" width="80">
				<template v-slot="{ row }">
					<span v-if="row.mutexFlag=='0'">否</span>
					<span v-if="row.mutexFlag=='1'">是</span>
				</template>
			</el-table-column>
			<el-table-column prop="notTime" label="无期限" width="80">
				<template v-slot="{ row }">
					<span v-if="row.notTime=='0'">否</span>
					<span v-if="row.notTime=='1'">是</span>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { SearchKeyType } from "./searchType";
import SetDrop from "./goodsComp/SetDrop.vue";
import { ApiSkuType, GoodDetailInfo } from "../goodType";
import { pageShopVerifyCode,exportShopPassTicketCode, } from "@/api/certificateApi/certificateApi"
import { pagePackageGoodsCodeDetail,exportWriteOffDetail } from "@/api/package/package"

// import SetDrop from "@/views/customer/common/SetDrop.vue";
@Component({
	components: {
		SetDrop
	}
})
export default class pagingList extends Vue {
	@Prop({})
	changeId!: string;

	@Watch("changeId")
	getSaleMode() {
		// this.searchType.status = this.changeId;
		console.log('bbbbbbbb', this.searchType, this.changeId);

		// this.getProduct();
	}
	searchType = {
		current: 1,
		size: 10
	} as SearchKeyType;
	ticketList = []
	total = 0;
	tableCheckedItem = [];
	get itemDropList() {
		return (row: GoodDetailInfo) => {
			return [
				{
					text: "删除",
					command: "delete",
					show: true,
					disabled: false
				},
				{
					text: "停用",
					command: "deactivate",
					show: true,
					disabled: false
				},
				{
					text: "编辑",
					command: "edit",
					show: true,
					disabled: false
				},
				{
					text: "审核",
					command: "examine",
					show: true,
					disabled: false
				}
			];
		};
	}
	mounted() {
		// this.searchType.status = this.changeId;
		// console.log("进入到goodlist",);
		//  加载搜索缓存
		var cache = JSON.parse(
			localStorage.getItem("cache_writeOffList_search_form") || "{}"
		);
		console.log('列表查询参数11', this.searchType, cache);
		this.searchType = Object.assign(this.searchType, cache) as SearchKeyType;

		this.getPageTicket();
	}
	getPageTicket() {
		// 删除请求链接里面的空值
		for (const key in this.searchType) {
			var value = Object.keys(this.searchType);  // value
			console.log(',,,,', key);
			if (!this.searchType[key]) {
				this.$delete(this.searchType, key)
			}
		}
		pagePackageGoodsCodeDetail(this.searchType).then((res) => {
			this.ticketList = res.data.records;
			this.total = res.data.total;
			this.$emit("getShowProList", this.ticketList);
			console.log('eeeeeeeee获取商品列表e', this.total, res.data.list);
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	exportPageTicket() {
		// 删除请求链接里面的空值
		for (const key in this.searchType) {
			var value = Object.keys(this.searchType);  // value
			console.log(',,,,', key);
			if (!this.searchType[key]) {
				this.$delete(this.searchType, key)
			}
		}
    exportWriteOffDetail(this.searchType).then((res) => {
			console.log(res);
			var blob = new Blob([res.data], {
                type: "application/x-msdownload;charset=UTF-8",
            });
            // 创建一个blob的对象链接
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            // 把获得的blob的对象链接赋值给新创建的这个 a 链接
            let now = new Date();
            let timestamp = now.getTime();
            link.setAttribute('download', '核销明细'+timestamp+'.xls'); // 设置下载文件名
            document.body.appendChild(link);
            // 使用js点击这个链接
            link.click();
		}).catch((err) => {
			this.$message.error(err)
		})

	}
	/**
	 * 获取下拉框
	 */
	getDropdown(val: string | number, row: GoodDetailInfo) {
		// if (Number(val) > 9) {
		console.log('获取下拉框', val, ',', row);
		if (val == 'edit') {
			this.getGoedit(row.id)
		}
		// if(val=='deactivate'){
		//   this.getDeactivateIntegral(row.id)
		// }
		// if(val=='detail'){
		//   this.pointsTab(row)
		// }

	}
	// 编辑通惠证
	getGoedit(id: string) {
		// this.$router.push({query:})
		this.$router.push({
			name: "AddCertificate",
			params: { id: String(id) },
			query: { id: id }
		});
	}

}
</script>

<style lang="scss" scoped>
.mouseEnter {
	// background-color: red;
	border: 1px solid #ecf6ff;
}

.mouseEnter:hover {
	// background-color: green;
	border: 1px solid #d7e0e8;
}

.pop--button {
	display: flex;
	justify-content: flex-end;
	margin-right: 10px;
}

.goodList {
	width: 200px;
	display: flex;
	// justify-content: center;
	text-align: center;
	padding-right: 20px;
	overflow: hidden;




}

.upDown {
	display: flex;
	align-items: center;
	justify-content: center;

	&__goodUp {
		display: flex;
		width: 50px;
		height: 20px;
		justify-content: center;
		align-items: center;
		border-radius: 4px;
		color: white;
		margin-right: 10px;
	}

	&__goodDown {
		margin-left: 10px;
		color: #2d8cf0;
		cursor: pointer;
	}
}

.commandClass {
	height: 150px;
	overflow: overlay;
}

.commandClass::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
	border-radius: 10px;
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 0;
	background: rgba(0, 0, 0, 0);
}

.center {
	display: flex;
	justify-content: center;
	font-size: 30px;
	font-weight: 700;
}

.digTitle {
	font-size: 17px;
	font-weight: bold;
}
</style>
  